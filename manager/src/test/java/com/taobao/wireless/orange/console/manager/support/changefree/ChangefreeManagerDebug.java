package com.taobao.wireless.orange.console.manager.support.changefree;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.goc.changefree.model.ChangeBaseResponse;
import com.taobao.wireless.orange.console.BaseManagerDebug;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ChangefreeManagerDebug extends BaseManagerDebug {

    @Autowired
    private ChangefreeManagerV2 changefreeManagerV2;

    private static final String TEST_APP_KEY = "60044370";
    private static final String TEST_NAMESPACE_NAME = "orange_test";
    private static final String TEST_NAMESPACE_ID = "a5e3c06054244bb19bf9de51ba9f74d5";
    private static final String TEST_NAMESPACE_VERSION = "2120220105110414167";
    private static final String TEST_EMPID = "39753";

    @Test
    public void testSepIn() throws Exception {

        String namespaceId = TEST_NAMESPACE_ID;
        String version = TEST_NAMESPACE_VERSION;
        String empId = TEST_EMPID;
        ChangeBaseResponse response = changefreeManagerV2.stepInBeforeChange(namespaceId, version, true, empId);
        System.out.println(">>>>>" + JSONObject.toJSONString(response));
        //Assert.assertTrue(resDto!=null);


    }
}
