package com.taobao.wireless.orange.console.manager.support.changefree;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.goc.changefree.model.ChangeQueryRes;
import com.taobao.wireless.orange.console.manager.BaseMockUnitManager;
import com.taobao.wireless.orange.console.manager.NamespaceVersionManager;
import com.taobao.wireless.orange.core.service.model.NamespaceVersionBO;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;

import static org.mockito.Mockito.when;

public class ChangefreeManagerV2Test extends BaseMockUnitManager {

    @Autowired
    private ChangefreeManagerV2 changefreeManagerV2;

    @Test
    public void doTestParse() {
        String orderId = "5115804";
        //未提单
        //{"apply_order_url":"https://aicf.alibaba-inc.com/aicf/grayscale/hm?publishInfoDownId=187457340","audit_order_url":"https://aicf.alibaba-inc.com/aicf/order_global_apply/187457340","check_status_enum":"CHECK_HOLD","order_detail_url":"","ruleList":[{"innerRuleUrl":"https://aicf.alibaba-inc.com/aicf/rule/1626","reason":"节假日、节假日前一天以及其它日期非10:00-17:00时间段原则上不允许发布，若要发布需要提单走审批","rule_name":"闲鱼紧急发布卡口-闲鱼fy23-紧急变更管控"},{"innerRuleUrl":"https://aicf.alibaba-inc.com/aicf/rule/1632","reason":"日常变更走cf卡口","rule_name":"闲鱼日常发布卡口-闲鱼fy23-日常变更管控"},{"innerRuleUrl":"https://aicf.alibaba-inc.com/aicf/rule/232","reason":"各BU稳定小组要求本BU的所有变更全部卡住，点击左下角的按钮【提交审批单】。","rule_name":"orange-console默认规则-BU稳定性小组要求所有变更都经过审批"}],"source_order_id":"12431167_0f4aa6acb69e4e6185559797f7c9ed2c_2120220916150907631_1663312325399","white":false}
        String data1 = "{\"apply_order_url\":\"https://aicf.alibaba-inc.com/aicf/grayscale/hm?publishInfoDownId=187457340\",\"audit_order_url\":\"https://aicf.alibaba-inc.com/aicf/order_global_apply/187457340\",\"check_status_enum\":\"CHECK_HOLD\",\"order_detail_url\":\"\",\"ruleList\":[{\"innerRuleUrl\":\"https://aicf.alibaba-inc.com/aicf/rule/1626\",\"reason\":\"节假日、节假日前一天以及其它日期非10:00-17:00时间段原则上不允许发布，若要发布需要提单走审批\",\"rule_name\":\"闲鱼紧急发布卡口-闲鱼fy23-紧急变更管控\"},{\"innerRuleUrl\":\"https://aicf.alibaba-inc.com/aicf/rule/1632\",\"reason\":\"日常变更走cf卡口\",\"rule_name\":\"闲鱼日常发布卡口-闲鱼fy23-日常变更管控\"},{\"innerRuleUrl\":\"https://aicf.alibaba-inc.com/aicf/rule/232\",\"reason\":\"各BU稳定小组要求本BU的所有变更全部卡住，点击左下角的按钮【提交审批单】。\",\"rule_name\":\"orange-console默认规则-BU稳定性小组要求所有变更都经过审批\"}],\"source_order_id\":\"12431167_0f4aa6acb69e4e6185559797f7c9ed2c_2120220916150907631_1663312325399\",\"white\":false}";
        doTestParse(data1, null);

        //已提单
        //{"apply_order_url":"https://aicf.alibaba-inc.com/aicf/grayscale/hm?publishInfoDownId=187457340","audit_order_url":"https://aicf.alibaba-inc.com/aicf/order_global_apply/187457340","check_status_enum":"CHECK_HOLD","order_detail_url":"https://aicf.alibaba-inc.com/aicf/approval/detail/5115804","order_status_enum":"ORDER_APPROVING","ruleList":[{"innerRuleUrl":"https://aicf.alibaba-inc.com/aicf/rule/1626","reason":"节假日、节假日前一天以及其它日期非10:00-17:00时间段原则上不允许发布，若要发布需要提单走审批","rule_name":"闲鱼紧急发布卡口-闲鱼fy23-紧急变更管控"},{"innerRuleUrl":"https://aicf.alibaba-inc.com/aicf/rule/1632","reason":"日常变更走cf卡口","rule_name":"闲鱼日常发布卡口-闲鱼fy23-日常变更管控"},{"innerRuleUrl":"https://aicf.alibaba-inc.com/aicf/rule/232","reason":"各BU稳定小组要求本BU的所有变更全部卡住，点击左下角的按钮【提交审批单】。","rule_name":"orange-console默认规则-BU稳定性小组要求所有变更都经过审批"}],"source_order_id":"12431167_0f4aa6acb69e4e6185559797f7c9ed2c_2120220916150907631_1663312325399","white":false}
        String data2 = "{\"apply_order_url\":\"https://aicf.alibaba-inc.com/aicf/grayscale/hm?publishInfoDownId=187457340\",\"audit_order_url\":\"https://aicf.alibaba-inc.com/aicf/order_global_apply/187457340\",\"check_status_enum\":\"CHECK_HOLD\",\"order_detail_url\":\"https://aicf.alibaba-inc.com/aicf/approval/detail/5115804\",\"order_status_enum\":\"ORDER_APPROVING\",\"ruleList\":[{\"innerRuleUrl\":\"https://aicf.alibaba-inc.com/aicf/rule/1626\",\"reason\":\"节假日、节假日前一天以及其它日期非10:00-17:00时间段原则上不允许发布，若要发布需要提单走审批\",\"rule_name\":\"闲鱼紧急发布卡口-闲鱼fy23-紧急变更管控\"},{\"innerRuleUrl\":\"https://aicf.alibaba-inc.com/aicf/rule/1632\",\"reason\":\"日常变更走cf卡口\",\"rule_name\":\"闲鱼日常发布卡口-闲鱼fy23-日常变更管控\"},{\"innerRuleUrl\":\"https://aicf.alibaba-inc.com/aicf/rule/232\",\"reason\":\"各BU稳定小组要求本BU的所有变更全部卡住，点击左下角的按钮【提交审批单】。\",\"rule_name\":\"orange-console默认规则-BU稳定性小组要求所有变更都经过审批\"}],\"source_order_id\":\"12431167_0f4aa6acb69e4e6185559797f7c9ed2c_2120220916150907631_1663312325399\",\"white\":false}";
        doTestParse(data2, orderId);


        //审批成功
        //{"apply_order_url":"https://aicf.alibaba-inc.com/aicf/grayscale/hm?publishInfoDownId=187457340","check_status_enum":"CHECK_PASS","order_detail_url":"https://aicf.alibaba-inc.com/aicf/approval/detail/5115804","order_status_enum":"ORDER_PASS","source_order_id":"12431167_0f4aa6acb69e4e6185559797f7c9ed2c_2120220916150907631_1663312325399","white":false}
        String data3 = "{\"apply_order_url\":\"https://aicf.alibaba-inc.com/aicf/grayscale/hm?publishInfoDownId=187457340\",\"check_status_enum\":\"CHECK_PASS\",\"order_detail_url\":\"https://aicf.alibaba-inc.com/aicf/approval/detail/5115804\",\"order_status_enum\":\"ORDER_PASS\",\"source_order_id\":\"12431167_0f4aa6acb69e4e6185559797f7c9ed2c_2120220916150907631_1663312325399\",\"white\":false}";

        doTestParse(data3, orderId);

    }

    private void doTestParse(String res, String expected) {
        ChangeQueryRes obj = JSONObject.parseObject(res, ChangeQueryRes.class);
        String orderId = ChangefreeManagerV2.parseOrderId(obj);
        Assert.assertEquals(expected, orderId);
    }

    @Test
    public void testGetCFRollbackResourceId() {
        // 修改兜底策略
        NamespaceVersionBO versionBO = new NamespaceVersionBO();
        versionBO.setNamespaceId("cdea5f74c7a24c36b64e4b56f56b72f4");
        versionBO.setVersion("2220241107111138583");
        versionBO.setAppKey("60035014");
        versionBO.setAppVersion("*");
        versionBO.setIsAvailable("n");
        versionBO.setStatus(0);
        versionBO.setLoadLevel(0);
        versionBO.setSource(0);
        versionBO.setStrategy(null);
        versionBO.setOverwriteStrategyVersions("");
        versionBO.setVersions("2220240913014530788,-");
        versionBO.setOfflines("{\"2220240909220430839\":1}");
        versionBO.setName("ruanying_test_custom");

        Object rollbackResourceId = ReflectionTestUtils.invokeMethod(changefreeManagerV2, "getCFRollbackResourceId", versionBO);
        Assert.assertEquals("修改兜底策略", "nce38f0e409c6b42e68953fb2809f5379b.json", rollbackResourceId);

        // 修改非兜底策略
        versionBO.setVersion("2220241107113100274");
        versionBO.setVersions("-,2220240909220430839");
        versionBO.setStrategy("app_ver>7.3.1");
        versionBO.setOfflines("{\"2220240913014530788\":3}");
        rollbackResourceId = ReflectionTestUtils.invokeMethod(changefreeManagerV2, "getCFRollbackResourceId", versionBO);
        Assert.assertEquals("修改非兜底策略", "nc14cd48181e044b7d8a9432e595378c15.json", rollbackResourceId);

        // 新增非兜底策略
        versionBO.setVersion("2220241107113414782");
        versionBO.setVersions("-,2220240913014530788,2220240909220430839");
        versionBO.setPreviousResourceId("nc14cd48181e044b7d8a9432e595378c15.json");
        versionBO.setStrategy("app_ver>7.3.2");
        versionBO.setOfflines("{}");
        rollbackResourceId = ReflectionTestUtils.invokeMethod(changefreeManagerV2, "getCFRollbackResourceId", versionBO);
        Assert.assertEquals("新增非兜底策略", "nc14cd48181e044b7d8a9432e595378c15.json", rollbackResourceId);

        // 删除&修改兜底策略
        versionBO.setVersion("2220241107113811872");
        versionBO.setVersions("2220240913014530788,-");
        versionBO.setStrategy(null);
        versionBO.setPreviousResourceId("nc31413b072e06454fa6c2addb2c071809.json");
        versionBO.setOfflines("{\"2220240909220430839\":1,\"2220241107113414782\":4}");
        rollbackResourceId = ReflectionTestUtils.invokeMethod(changefreeManagerV2, "getCFRollbackResourceId", versionBO);
        Assert.assertEquals("删除&修改兜底策略", "nce38f0e409c6b42e68953fb2809f5379b.json", rollbackResourceId);

        // 删除&修改非兜底策略
        versionBO.setVersion("2220241107113548739");
        versionBO.setVersions("-,2220240909220430839");
        versionBO.setStrategy("app_ver>7.3.1");
        versionBO.setOfflines("{\"2220240913014530788\":3,\"2220241107113414782\":4}");
        rollbackResourceId = ReflectionTestUtils.invokeMethod(changefreeManagerV2, "getCFRollbackResourceId", versionBO);
        Assert.assertEquals("删除&修改非兜底策略", "nc14cd48181e044b7d8a9432e595378c15.json", rollbackResourceId);

        // 新增兜底策略
        versionBO.setNamespaceId("768505bcac0a45bb969de62dc82d0d64");
        versionBO.setAppKey("60035015");
        versionBO.setVersion("2120241107114219413");
        versionBO.setPreviousResourceId(null);
        versionBO.setVersions("-");
        versionBO.setStrategy(null);
        versionBO.setOfflines("{}");
        rollbackResourceId = ReflectionTestUtils.invokeMethod(changefreeManagerV2, "getCFRollbackResourceId", versionBO);
        Assert.assertNull("新增兜底策略", rollbackResourceId);

    }
}
