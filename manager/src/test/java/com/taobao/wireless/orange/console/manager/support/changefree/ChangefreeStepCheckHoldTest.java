package com.taobao.wireless.orange.console.manager.support.changefree;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.goc.changefree.model.ChangeBaseResponse;
import com.taobao.wireless.orange.console.manager.config.OrangeConfig;
import com.taobao.wireless.orange.console.manager.manager.record.RecordManagerV2;
import com.taobao.wireless.orange.console.manager.manager.record.check.impl.ChangefreeStepCheckHold;
import com.taobao.wireless.orange.console.manager.manager.record.model.check.CheckContext;
import com.taobao.wireless.orange.console.manager.manager.record.model.check.CheckDetailDO;
import com.taobao.wireless.orange.console.manager.support.diamond.DiamondCommonDataManager;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.Silent.class)
public class ChangefreeStepCheckHoldTest {

    @InjectMocks
    private ChangefreeStepCheckHold changefreeStepCheckHold;

    @Mock
    private OrangeConfig orangeConfig;
    @Mock
    private ChangefreeManagerV2 changefreeManagerV2;
    @Mock
    private DiamondCommonDataManager diamondCommonDataManager;
    @Mock
    private RecordManagerV2 recordManagerV2;

    private static final String TEST_APP_KEY = "531772";
    private static final String TEST_NAMESPACE_NAME = "orange_test";
    private static final String TEST_NAMESPACE_ID = "cfdfa429966f47dba090ec6663272380";
    private static final String TEST_NAMESPACE_VERSION = "2220220110114245942";
    private static final String TEST_EMPID = "39753";
    public static final Map<String, String> MOCK_RESPONSE_MAP = new HashMap<>();
    public static final Map<String, String> MOCK_RET_MAP = new HashMap<>();

    static {
        //链接失败
        MOCK_RESPONSE_MAP.put("link", "{\"msg\":\"ChangeFreeClient do execute error\",\"successful\":false}\n");
        MOCK_RET_MAP.put("link", "{\"message\":\"CF_STEP_ERROR_BIZ(null,ChangeFreeClient do execute error)\",\"provider\":\"CF_STEP\",\"skipSupport\":\"ANY\",\"status\":\"EXCEPTION\"}");
        //参数异常
        MOCK_RESPONSE_MAP.put("param", "{\"body\":\"{\\\"change_object\\\":\\\"{\\\\\\\"appKey\\\\\\\":\\\\\\\"27848853\\\\\\\",\\\\\\\"appKeyName\\\\\\\":\\\\\\\"星缕测试应用1-android\\\\\\\",\\\\\\\"url\\\\\\\":\\\\\\\"https://orange-console-pre.alibaba-inc.com/index.htm#/namespace/version/detail/9a5f34946010459a80bfb7d8f7d3744c-2120220602090533800\\\\\\\",\\\\\\\"extraInfo\\\\\\\":{\\\\\\\"namespaceName\\\\\\\":\\\\\\\"orange_test_lanyin\\\\\\\"},\\\\\\\"primary\\\\\\\":\\\\\\\"appKey\\\\\\\"}\\\",\\\"change_start_time\\\":1654137534371,\\\"executor_emp_id\\\":\\\"39753\\\",\\\"change_impact\\\":\\\"\\\",\\\"change_object_type\\\":\\\"CONFIG\\\",\\\"gray_strategy\\\":\\\"{\\\\\\\"currentBatch\\\\\\\":1.0}\\\",\\\"source_order_id\\\":\\\"\\\",\\\"auth_key\\\":\\\"5dbd32ee-f1d8-431d-a2cb-705117f899f4\\\",\\\"change_env\\\":\\\"PRE_PUBLISH\\\"}\",\"code\":\"400\",\"msg\":\"parameter illegal\",\"sub_code\":\"PARAM_NULL\",\"sub_msg\":\"sourceOrderId should not be null.\",\"successful\":false}");
        MOCK_RET_MAP.put("param", "{\"message\":\"CF_STEP_ERROR_BIZ(400,parameter illegal)\",\"provider\":\"CF_STEP\",\"skipSupport\":\"ANY\",\"status\":\"EXCEPTION\"}");

        //封网阻断
        MOCK_RESPONSE_MAP.put("recheck", "{\"body\":\"{\\\"gray_strategy\\\":\\\"{\\\\\\\"currentBatch\\\\\\\":1.0}\\\",\\\"change_observe_result\\\":\\\"null\\\",\\\"stepin_msg\\\":\\\"orange封网管控验证ALL，建议先暂停当前生产变更，待结束后继续，如需继续变更，请发起变更审批，通过后继续。\\\\n\\\"" +
                ",\\\"approval_url\\\":\\\"https://aicf.alibaba.net/aicf/grayscale/hm?publishInfoDownId=2070771\\\"" +
                ",\\\"stepin_html_msg\\\":\\\"orange封网管控验证ALL，建议先暂停当前生产变更，待结束后继续，如需继续变更，请发起变更审批，通过后继续。<a href='https://aicf.alibaba.net/aicf/grayscale/hm?publishInfoDownId=2070771'>发起审批</a>\\\\n\\\"" +
                ",\\\"orderUrl\\\":\\\"https://aicf.alibaba.net/aicf/grayscale/hm?publishInfoDownId=2070771\\\"" +
                ",\\\"able_skip\\\":false" +
                ",\\\"status\\\":\\\"STEPIN_HOLD\\\"}\",\"code\":\"0\",\"msg\":\"\",\"successful\":true}");
        MOCK_RET_MAP.put("recheck", "{\"approvalUrl\":\"https://aicf.alibaba.net/aicf/grayscale/hm?publishInfoDownId=2070771\",\"detailUrl\":\"https://aicf.alibaba.net/aicf/grayscale/hm?publishInfoDownId=2070771\",\"message\":\"orange封网管控验证ALL，建议先暂停当前生产变更，待结束后继续，如需继续变更，请发起变更审批，通过后继续。\\n\",\"provider\":\"CF_STEP\",\"skipSupport\":\"NONE\",\"status\":\"HOLD\"}");

        //灰度阻断
        MOCK_RESPONSE_MAP.put("gray", "{\"body\":\"{\\\"gray_strategy\\\":\\\"{\\\\\\\"currentBatch\\\\\\\":1.0}\\\",\\\"change_observe_result\\\":\\\"null\\\",\\\"stepin_msg\\\":\\\"1. 灰度第1批停留时长要求10分钟以上，剩余等待时长10分钟。规则负责人：108441\\n2. 建议您按要求停留并进行有效观测，如遇紧急情况，在保障稳定性的前提下，可点击强制恢复进行下一批发布。\\n\\\\n\\\"" +
                ",\\\"approval_url\\\":null,\\\"stepin_html_msg\\\":\\\"<br/><ul><b><li>1. 灰度第1批停留时长要求10分钟以上，剩余等待时长10分钟。规则负责人：108441\\n2. 建议您按要求停留并进行有效观测，如遇紧急情况，在保障稳定性的前提下，可点击强制恢复进行下一批发布。</li></b><b><li></li></b></ul>\\\\n\\\"" +
                ",\\\"orderUrl\\\":\\\"https://g.alibaba-inc.com/aicf/observe/4675374\\\"" +
                ",\\\"able_skip\\\":true" +
                ",\\\"status\\\":\\\"STEPIN_HOLD\\\"}\",\"code\":\"0\",\"msg\":\"\",\"successful\":true}");
        MOCK_RET_MAP.put("gray", "{\"detailUrl\":\"https://g.alibaba-inc.com/aicf/observe/4675374\",\"message\":\"1. 灰度第1批停留时长要求10分钟以上，剩余等待时长10分钟。规则负责人：108441\\n2. 建议您按要求停留并进行有效观测，如遇紧急情况，在保障稳定性的前提下，可点击强制恢复进行下一批发布。\\n\\n\",\"provider\":\"CF_STEP\",\"skipSupport\":\"ANY\",\"status\":\"HOLD\"}");
        //观测阻断
        MOCK_RESPONSE_MAP.put("observe", "{\"body\":\"{\\\"gray_strategy\\\":\\\"{\\\\\\\"currentBatch\\\\\\\":1.0}\\\",\\\"change_observe_result\\\":\\\"[{\\\\\\\"observeMsg\\\\\\\":\\\\\\\"16:07分，线上出现供应链测试成功率下跌测试的P4故障，建议您先检查您正在进行的变更是否有关，如可能有关系建议先暂停变更，甚至回滚，如确定无关，您可以继续发布\\\\\\\",\\\\\\\"observeName\\\\\\\":\\\\\\\"供应链测试成功率下跌测试\\\\\\\",\\\\\\\"observePlatform\\\\\\\":\\\\\\\"FAULT\\\\\\\",\\\\\\\"observeStatus\\\\\\\":\\\\\\\"OBSERVE_HOLD\\\\\\\",\\\\\\\"observeStrategyId\\\\\\\":42,\\\\\\\"observeTime\\\\\\\":\\\\\\\"16:07\\\\\\\",\\\\\\\"observeUrl\\\\\\\":\\\\\\\"https://g.alibaba-inc.com/ormFailure/workbench/failure/2019122400000015001\\\\\\\"}]\\\"" +
                ",\\\"stepin_msg\\\":\\\"16:07分，线上出现供应链测试成功率下跌测试的P4故障，建议您先检查您正在进行的变更是否有关，如可能有关系建议先暂停变更，甚至回滚，如确定无关，您可以继续发布\\\\n\\\"" +
                ",\\\"approval_url\\\":null" +
                ",\\\"stepin_html_msg\\\":\\\"16:07分，线上出现供应链测试成功率下跌测试的P4故障，建议您先检查您正在进行的变更是否有关，如可能有关系建议先暂停变更，甚至回滚，如确定无关，您可以继续发布。<a href='https://g.alibaba-inc.com/aicf/observe/4675374'>查看详情</a>。\\\\n\\\"" +
                ",\\\"orderUrl\\\":\\\"https://g.alibaba-inc.com/aicf/observe/4675374\\\"" +
                ",\\\"able_skip\\\":true" +
                ",\\\"status\\\":\\\"STEPIN_HOLD\\\"}\"" +
                ",\"code\":\"0\",\"msg\":\"\",\"successful\":true}");
        MOCK_RET_MAP.put("observe", "{\"detailUrl\":\"https://g.alibaba-inc.com/aicf/observe/4675374\",\"message\":\"16:07分，线上出现供应链测试成功率下跌测试的P4故障，建议您先检查您正在进行的变更是否有关，如可能有关系建议先暂停变更，甚至回滚，如确定无关，您可以继续发布\\n\",\"provider\":\"CF_STEP\",\"skipSupport\":\"ANY\",\"status\":\"HOLD\"}");

        //通过
        MOCK_RESPONSE_MAP.put("pass", "{\"body\":\"{\\\"gray_strategy\\\":\\\"{\\\\\\\"currentBatch\\\\\\\":1.0}\\\",\\\"change_observe_result\\\":\\\"null\\\",\\\"stepin_msg\\\":\\\"\\\"" +
                ",\\\"stepin_html_msg\\\":\\\"\\\",\\\"status\\\":\\\"STEPIN_PASS\\\"" +
                ",\\\"orderUrl\\\":\\\"https://aicf.alibaba-inc.com/aicf/orderDetailNew/177494397\\\"" +
                ",\\\"ableSkip\\\":true}\"" +
                ",\"code\":\"0\",\"msg\":\"\",\"successful\":true}");
        MOCK_RET_MAP.put("pass", "{\"provider\":\"CF_STEP\",\"skipSupport\":\"ANY\",\"status\":\"PASSED\"}");

    }

    @Test
    public void testStepIn() throws Exception {
        Mockito.doReturn(true).when(orangeConfig).isDaily();
        Mockito.doReturn(true).when(diamondCommonDataManager).enableCheckHold(anyString(), anyString(), anyString());
        CheckContext checkContext = getCheckContext();
        //第一步：检查有sourceOrderId
        Mockito.doReturn("sourceOrderId").when(recordManagerV2).getLatestChangefreeId(anyString(), anyString());
        for (String key : MOCK_RESPONSE_MAP.keySet()) {
            // System.out.println("___________Begin________________");
            // System.out.println(">>>"+key);
            String response = MOCK_RESPONSE_MAP.get(key);
            // System.out.println(response);
            Mockito.doReturn(getMockChangeBaseResponse(response)).when(changefreeManagerV2).stepInBeforeChange(anyString(), anyString(), anyBoolean(), anyString());
            CheckDetailDO detailDO = changefreeStepCheckHold.doCheck(checkContext);
            //System.out.println(JSONObject.toJSONString(detailDO));
            Assert.assertTrue(detailDO != null);
            CheckDetailDO expect = JSONObject.parseObject(MOCK_RET_MAP.get(key), CheckDetailDO.class);
            Assert.assertEquals(expect, detailDO);
            //System.out.println("___________End________________");
        }

        //第二步：检查无sourceOrderId
        Mockito.doReturn(null).when(recordManagerV2).getLatestChangefreeId(anyString(), anyString());
        CheckDetailDO nullRet = changefreeStepCheckHold.doCheck(checkContext);
        Assert.assertNull(nullRet);
    }

    private CheckContext getCheckContext() {
        CheckContext checkContext = new CheckContext();
        //checkContext.setCheckProcess(CheckProvider.CF_STEP);
        checkContext.setNamespaceId(TEST_NAMESPACE_ID);
        checkContext.setAppKey(TEST_APP_KEY);
        checkContext.setName(TEST_NAMESPACE_NAME);
        checkContext.setRecordId(12L);
        // checkContext.setRefId(TEST_SOURCE_ORDER_ID);
        checkContext.setVersion(TEST_NAMESPACE_VERSION);
        checkContext.setOperator(TEST_EMPID);
        return checkContext;
    }

    public ChangeBaseResponse getMockChangeBaseResponse(String response) {

        return JSONObject.parseObject(response, ChangeBaseResponse.class);
    }
}
