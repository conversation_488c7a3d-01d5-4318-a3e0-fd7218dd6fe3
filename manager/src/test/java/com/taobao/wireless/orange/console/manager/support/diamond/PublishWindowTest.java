package com.taobao.wireless.orange.console.manager.support.diamond;

import java.text.ParseException;
import java.util.*;

import com.taobao.wireless.orange.console.manager.impl.DiamondPublishWindowManager;
import com.taobao.wireless.orange.console.manager.model.PublishWindow;
import com.taobao.wireless.orange.console.manager.model.PublishWindow.TimeRange;
import com.taobao.wireless.orange.console.manager.model.PublishWindowParser;
import com.taobao.wireless.orange.console.manager.util.DateUtil;
import com.taobao.wireless.orange.core.type.Source;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Test;

import static com.taobao.wireless.orange.console.manager.util.DateUtil.FORMAT_SS;
import static com.taobao.wireless.orange.core.type.Source.ORANGE;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @create 2018/9/26 下午4:33
 */
public class PublishWindowTest {
    private String app = "testApp";
    private String namespace = "testNamespace";
    private String app1 = "testApp1";
    private String namespace1 = "testNamespace1";
    private Source source = ORANGE;

    @Test
    public void testNotInCloseRangeAll() {
        EnumMap<Source, List<TimeRange>> sourceMap = new EnumMap<>(Source.class);
        sourceMap.put(ORANGE, Arrays.asList(outOfRangeDate()));
        PublishWindow pw = new PublishWindow(sourceMap, null, null);
        assertTrue(pw.couldPublish(app, namespace, source));
        assertTrue(pw.couldPublish(app1, namespace1, source));
    }

    @Test
    public void testInCloseRangeAll() {
        PublishWindow pw = new PublishWindow(mockSourceMap(), null, null);
        assertFalse(pw.couldPublish(app, namespace, source));
        assertFalse(pw.couldPublish(app1, namespace1, source));
    }

    @Test
    public void testCloseApp() {
        PublishWindow pw = new PublishWindow(mockSourceMap(), null, mockMap("*@testApp"));
        assertFalse(pw.couldPublish(app, namespace, source));
        assertTrue(pw.couldPublish(app1, namespace, source));
    }

    @Test
    public void testCloseNamespace() {
        PublishWindow pw = new PublishWindow(mockSourceMap(), null, mockMap("testNamespace@testApp"));
        assertFalse(pw.couldPublish(app, namespace, source));
        assertTrue(pw.couldPublish(app, namespace1, source));
    }

    @Test
    public void testAppInWhite() {
        PublishWindow pw = new PublishWindow(mockSourceMap(), mockMap("*@testApp"), null);
        assertTrue(pw.couldPublish(app, namespace, source));
        assertFalse(pw.couldPublish(app1, namespace, source));
    }

    @Test
    public void testNamespaceInWhite() {
        PublishWindow pw = new PublishWindow(mockSourceMap(), mockMap("testNamespace@testApp"), null);
        assertTrue(pw.couldPublish(app, namespace, source));
        assertFalse(pw.couldPublish(app, namespace1, source));
        assertFalse(pw.couldPublish(app1, namespace1, source));
    }

    /**
     * app同时在黑白名单，允许发布
     */
    @Test
    public void testAppInWhiteAndBlackBoth() {
        PublishWindow pw = new PublishWindow(mockSourceMap(), mockMap("*@testApp"), mockMap("*@testApp"));
        assertTrue(pw.couldPublish(app, namespace, source));
        assertTrue(pw.couldPublish(app, namespace1, source));
        assertTrue(pw.couldPublish(app1, namespace1, source));
    }

    /**
     * namespace同时在黑白名单，允许发布
     */
    @Test
    public void testNamespaceInWhiteAndBlackBoth() {
        PublishWindow pw = new PublishWindow(mockSourceMap(), mockMap("testNamespace@testApp"),
                mockMap("testNamespace@testApp"));
        assertTrue(pw.couldPublish(app, namespace, source));
        assertTrue(pw.couldPublish(app, namespace1, source));
        assertTrue(pw.couldPublish(app1, namespace1, source));
    }


    private static TimeRange inRangeDate() {
        LocalDate localDate = new LocalDate();
        LocalDate tom = localDate.plusDays(1);
        return new TimeRange(localDate.toDate(), tom.toDate());
    }

    private static TimeRange outOfRangeDate() {
        LocalDate localDate = new LocalDate();
        LocalDate from = localDate.minusDays(2);
        LocalDate to = localDate.minusDays(1);
        return new TimeRange(from.toDate(), to.toDate());
    }

    private static EnumMap<Source, List<TimeRange>> mockSourceMap() {
        EnumMap<Source, List<TimeRange>> sourceMap = new EnumMap<>(Source.class);
        sourceMap.put(ORANGE, Arrays.asList(inRangeDate()));
        return sourceMap;
    }

    private static Map<String, String> mockMap(String... keys) {
        Map map = new HashMap();
        for (String k : keys) {
            map.put(k, null);
        }
        return map;
    }


    private static PublishWindow mockPublishWindow() throws ParseException {
        String text = "ORANGE_API=20211110$23:30-20451111$01:30\n" +
                "PREPLAN=20211110$23:30-20451111$00:30\n" +
                "ORANGE=20211110$23:30-20451111$00:30\n" +
                "\n" +
                "##ott集群配置错误，目前指定中心集群熔断发布，联系人 影修\n" +
                "black_list=*@25108083,*@25020498,*@23299685,*@23164371,*@24889135";
        return getPublishWindow(text);
    }

    @Test
    public void testCouldPublish() {
        PublishWindow publishWindow = null;
        try {
            publishWindow = mockPublishWindow();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Assert.assertEquals(false, publishWindow.couldPublish("25108083", "orange", ORANGE));

    }


    @Test
    public void testMultipleTimes() throws Exception {
        String text = "#PREPLAN:预案平台,ORANGE:控制台,ORANGE_API:HSF接口\n" +
                "ORANGE_API=20200101$00:00-20240101$00:00\n" +
                "PREPLAN=20200101$00:00-20240101$00:00\n" +
                "ORANGE=20221031$19:30-20221031$20:30,20221110$19:30-20221110$20:30";
        PublishWindow pw = getPublishWindow(text);
        Date inTime1 = DateUtil.format("20221031200000", FORMAT_SS);
        Date inTime2 = DateUtil.format("20221110200000", FORMAT_SS);
        assertFalse(pw.couldPublish(inTime1, app, namespace, source));
        assertFalse(pw.couldPublish(inTime2, app, namespace, source));
        Date outTime1 = DateUtil.format("20221101200000", FORMAT_SS);
        assertTrue(pw.couldPublish(outTime1, app, namespace, source));
    }

    @Test
    public void testWhiteList() throws Exception {
        String text = "#PREPLAN:预案平台,ORANGE:控制台,ORANGE_API:HSF接口\n" +
                "ORANGE_API=20200101$00:00-20240101$00:00\n" +
                "PREPLAN=20200101$00:00-20240101$00:00\n" +
                "ORANGE=20221031$19:30-20221031$20:30\n" +
                "#白名单，空代表不指定，namespace@appKey，namespace为*表示全部\n" +
                "white_list=whileNamespace@whiteApp\n";
        PublishWindow pw = getPublishWindow(text);
        Date inTime = DateUtil.format("20221031200000", FORMAT_SS);
        assertFalse(pw.couldPublish(inTime, app, namespace, source));
        assertTrue(pw.couldPublish(inTime, "whiteApp", "whileNamespace", source));

        Date outTime = DateUtil.format("20221101200000", FORMAT_SS);
        assertTrue(pw.couldPublish(outTime, app, namespace, source));
        assertTrue(pw.couldPublish(outTime, "whiteApp", "whileNamespace", source));
    }

    private static PublishWindow getPublishWindow(String text) throws ParseException {
        Properties properties = DiamondPublishWindowManager.parseProperties(text);
        PublishWindow publishWindow = PublishWindowParser.parse(properties);
        return publishWindow;

    }
}
