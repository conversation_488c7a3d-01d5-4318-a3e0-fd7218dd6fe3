package com.taobao.wireless.orange.console.manager.support.user;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;

public class UserManagerTest {
    @Test
    public void testSet() {

        //Set<String > actionSet = new HashSet<>(Arrays.asList("123","456"));
        //System.out.println(Joiner.on(",").join(actionSet));
        List<String> list = Arrays.asList("789", "456", "123", "456");
        //即要保证有序，又要去重
        Set<String> orderSet = new LinkedHashSet<>(list);
        System.out.println(JSONObject.toJSONString(orderSet));

        List<String> ret = orderSet.stream().collect(Collectors.toList());
        System.out.println(JSONObject.toJSONString(ret));
    }


    @Test
    public void testAmpdHtppResult() {
        String result1 = "{\"errorCode\":\"biz_error\",\"success\":false,\"errorMsg\":\"combine can't authorized for access!\"}";
        //System.out.println(AmdpUserHTTP.parseUser(result1));

        String result2 = "{\"success\":true,\"content\":{\"dataRows\":[{\"dataFields\":[{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"deptNo\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"deptNo\",\"domainStandardFieldNameEn\":\"deptNo\",\"id\":302,\"value\":\"B0301\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"name\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"name\",\"domainStandardFieldNameEn\":\"name\",\"id\":135,\"value\":\"邵明芝\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"lastNameEn\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"lastNameEn\",\"domainStandardFieldNameEn\":\"lastNameEn\",\"id\":178,\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"deptEnName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"deptEnName\",\"domainStandardFieldNameEn\":\"deptEnName\",\"id\":5914,\"value\":\"Greater Taobao-Greater Taobao Technology-Terminal experience platform-Application Architecture\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"superNickName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"superNickName\",\"domainStandardFieldNameEn\":\"superNickName\",\"id\":202,\"value\":\"泽彬\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"primaryHavanaId\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"primaryHavanaId\",\"domainStandardFieldNameEn\":\"primaryHavanaId\",\"id\":5686,\"value\":\"443554617\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"deptShortName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"deptShortName\",\"domainStandardFieldNameEn\":\"deptShortName\",\"id\":5912,\"value\":\"应用服务技术\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"hrgNickName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"hrgNickName\",\"domainStandardFieldNameEn\":\"hrgNickName\",\"id\":210,\"value\":\"冬予\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"workStatus\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"workStatus\",\"domainStandardFieldNameEn\":\"workStatus\",\"id\":138,\"value\":\"A\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"englishName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"englishName\",\"domainStandardFieldNameEn\":\"englishName\",\"id\":153,\"value\":\"\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"firstNameEn\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"firstNameEn\",\"domainStandardFieldNameEn\":\"firstNameEn\",\"id\":179,\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"businessUnit\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"businessUnit\",\"domainStandardFieldNameEn\":\"businessUnit\",\"id\":5684,\"value\":\"TBTD\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"loginAccount\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"loginAccount\",\"domainStandardFieldNameEn\":\"loginAccount\",\"id\":147,\"value\":\"lanyin.smz\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"firstNameCn\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"firstNameCn\",\"domainStandardFieldNameEn\":\"firstNameCn\",\"id\":177,\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"hrgName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"hrgName\",\"domainStandardFieldNameEn\":\"hrgName\",\"id\":209,\"value\":\"刘冬冬\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"nickName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"nickName\",\"domainStandardFieldNameEn\":\"nickName\",\"id\":134,\"value\":\"兰茵\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"userId\",\"domainName\":\"员工域\",\"valueType\":\"INT\",\"name\":\"userId\",\"domainStandardFieldNameEn\":\"userId\",\"id\":5687,\"value\":65153,\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"havanaEntities\",\"domainName\":\"员工域\",\"valueType\":\"JSON\",\"name\":\"havanaEntities\",\"domainStandardFieldNameEn\":\"havanaEntities\",\"id\":5685,\"value\":\"null\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"superName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"superName\",\"domainStandardFieldNameEn\":\"superName\",\"id\":201,\"value\":\"许泽彬\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"userSign\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"userSign\",\"domainStandardFieldNameEn\":\"userSign\",\"id\":5688,\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"status\",\"domainName\":\"员工域\",\"valueType\":\"INT\",\"name\":\"status\",\"domainStandardFieldNameEn\":\"status\",\"id\":5823,\"value\":0,\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"buMail\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"buMail\",\"domainStandardFieldNameEn\":\"buMail\",\"id\":458,\"value\":\"<EMAIL>\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"superWorkNo\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"superWorkNo\",\"domainStandardFieldNameEn\":\"superWorkNo\",\"id\":199,\"value\":\"024523\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"hrgWorkNo\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"hrgWorkNo\",\"domainStandardFieldNameEn\":\"hrgWorkNo\",\"id\":207,\"value\":\"346622\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"available\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"available\",\"domainStandardFieldNameEn\":\"available\",\"id\":5683,\"value\":\"T\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"empType\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"empType\",\"domainStandardFieldNameEn\":\"empType\",\"id\":136,\"value\":\"正式\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"superOrderNum\",\"domainName\":\"员工域\",\"valueType\":\"LONG\",\"name\":\"superOrderNum\",\"domainStandardFieldNameEn\":\"superOrderNum\",\"id\":200,\"value\":\"0\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"userSignCode\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"userSignCode\",\"domainStandardFieldNameEn\":\"userSignCode\",\"id\":5689,\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"deptName\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"deptName\",\"domainStandardFieldNameEn\":\"deptName\",\"id\":5913,\"value\":\"大淘宝-大淘宝技术-终端体验平台-应用服务技术\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"hrgOrderNum\",\"domainName\":\"员工域\",\"valueType\":\"INT\",\"name\":\"hrgOrderNum\",\"domainStandardFieldNameEn\":\"hrgOrderNum\",\"id\":208,\"value\":\"0\",\"status\":\"success\"},{\"domainCode\":\"EMP_EMPLOYEE\",\"assetCode\":\"workNo\",\"domainName\":\"员工域\",\"valueType\":\"STRING\",\"name\":\"workNo\",\"domainStandardFieldNameEn\":\"workNo\",\"id\":133,\"value\":\"039753\",\"status\":\"success\"}]}]}}";
        System.out.println(AmdpUserHTTP.parseUser(result2));

    }
}
